const express = require('express');
const router = express.Router();
const userActivityRoutes = require('./userActivityRoutes');
const userGroupRoutes = require('./userGroupRoutes');
const authRoutes = require('./authRoutes');

// API路由
router.use('/api/user-activity', userActivityRoutes);
router.use('/api/user-groups', userGroupRoutes);
router.use('/api/auth', authRoutes);

// 首页路由
router.get('/', (req, res) => {
  res.json({
    message: '欢迎使用用户行为数据收集服务',
    version: '1.0.0'
  });
});

module.exports = router;
