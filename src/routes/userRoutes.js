// Rename this file to userRoutes.js
const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const { tokenAuth } = require('../middleware/tokenAuthMiddleware');

// 用户路由 - 所有路由都需要令牌认证
router.get('/', tokenAuth, userController.getAllUsers);
router.get('/:id', tokenAuth, userController.getUserById);
router.post('/', tokenAuth, userController.createUser);
// router.put('/:id', tokenAuth, userController.updateUser);
// router.delete('/:id', tokenAuth, userController.deleteUser);

module.exports = router;
