const express = require('express');
const router = express.Router();
const userGroupController = require('../controllers/userGroupController');
const { tokenAuth } = require('../middleware/tokenAuthMiddleware');

// 用户组路由 - 所有路由都需要令牌认证
router.get('/', tokenAuth, userGroupController.getAllUserGroups);
router.get('/:id', tokenAuth, userGroupController.getUserGroupById);
router.post('/', tokenAuth, userGroupController.createUserGroup);
// router.put('/:id', tokenAuth, userGroupController.updateUserGroup);
// router.delete('/:id', tokenAuth, userGroupController.deleteUserGroup);

module.exports = router;
