const express = require('express');
const router = express.Router();
const userActivityController = require('../controllers/userActivityController');
const { tokenAuth } = require('../middleware/tokenAuthMiddleware');

// 用户路由 - 所有路由都需要令牌认证
router.get('/', tokenAuth, userActivityController.getAllUserActivitys);
router.get('/:id', tokenAuth, userActivityController.getUserActivityById);
router.post('/', tokenAuth, userActivityController.createUserActivity);
// router.put('/:id', tokenAuth, userActivityController.updateUserActivity);
// router.delete('/:id', tokenAuth, userActivityController.deleteUserActivity);

module.exports = router;
