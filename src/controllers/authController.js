const EncryptionUtil = require('../utils/encryption');

/**
 * 认证控制器
 * 处理用户认证相关的逻辑
 */

/**
 * 测试GitLab认证
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.testAuth = (req, res) => {
  res.json({
    success: true,
    message: '认证成功',
    user: req.user
  });
};

/**
 * 获取AES加密的用户名令牌
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
exports.getAuthToken = (req, res) => {
  try {
    // 从 GitLab 用户信息中获取用户名
    const username = req.user.username || req.user.name || req.user.email;
    
    if (!username) {
      return res.status(400).json({
        success: false,
        message: '无法获取用户名'
      });
    }
    
    // 使用 AES-ECB 加密用户名
    const encryptedUsername = EncryptionUtil.encryptAES(username);
    
    // 返回加密后的用户名
    res.json({
      success: true,
      token: encryptedUsername,
			userName: username
    });
  } catch (error) {
    console.error('用户名加密失败:', error);
    res.status(500).json({
      success: false,
      message: '用户名加密失败'
    });
  }
};
