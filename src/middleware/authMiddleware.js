const axios = require('axios');
require('dotenv').config();

/**
 * GitLab 认证中间件
 * 使用 GitLab API 验证用户身份
 * 请求头格式: Authorization: Bearer <your_access_token>
 */
const gitlabAuth = async (req, res, next) => {
  try {
    // 获取请求头中的 Authorization
    const authHeader = req.headers.authorization;
    
    // 检查 Authorization 头是否存在
    if (!authHeader || !authHeader.startsWith('git')) {
      return res.status(401).json({
        success: false,
        message: '未提供访问令牌'
      });
    }
    
    // 提取访问令牌
    const [server,token] = authHeader.split(' ') ;
    
    // 检查令牌是否为空
    if (!token) {
      return res.status(401).json({
        success: false,
        message: '访问令牌不能为空'
      });
    }
    
    // 调用 GitLab API 验证用户身份
    const response = await axios.get(`${process.env.GITLAB_API_URL.replace("{server}",server)}/api/v4/user`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    // 如果请求成功，将用户信息添加到请求对象中
    if (response.data && response.data.id) {
      req.user = response.data;
      next();
    } else {
      return res.status(401).json({
        success: false,
        message: '无效的访问令牌'
      });
    }
  } catch (error) {
    console.error('GitLab 认证失败:', error.message);
    
    // 根据错误类型返回不同的响应
    if (error.response) {
      // GitLab API 返回的错误
      if (error.response.status === 401) {
        return res.status(401).json({
          success: false,
          message: '无效的访问令牌'
        });
      } else {
        return res.status(error.response.status).json({
          success: false,
          message: `GitLab API 错误: ${error.response.data.message || '未知错误'}`
        });
      }
    } else if (error.request) {
      // 请求发送但没有收到响应
      return res.status(503).json({
        success: false,
        message: 'GitLab API 服务不可用'
      });
    } else {
      // 请求设置时出错
      return res.status(500).json({
        success: false,
        message: '认证过程中发生错误'
      });
    }
  }
};

module.exports = {
  gitlabAuth
};
