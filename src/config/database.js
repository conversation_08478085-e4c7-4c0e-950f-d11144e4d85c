const mongoose = require('mongoose');
require('dotenv').config();

// MongoDB连接配置
const connectDB = async () => {
  try {
    // console.log(process.env)

    const conn = await mongoose.connect(process.env.MONGODB_URI, {
      // 连接选项
      user: process.env.MONGODB_USER,
      pass: process.env.MONGODB_PASSWORD,
      maxPoolSize: 10, // 连接池最大连接数
      serverSelectionTimeoutMS: 3000, // 服务器选择超时时间
      socketTimeoutMS: 5000, // Socket超时时间
      connectTimeoutMS: 3000, // 连接超时时间
    });
    console.log(`MongoDB连接成功: ${conn.connection.host}`);
    return conn;
  } catch (error) {
    console.error('MongoDB连接失败:', error.message);
    throw error;
  }
};

// 测试数据库连接
const testConnection = async () => {
  try {
    if (mongoose.connection.readyState === 1) {
      console.log('数据库连接成功');
    } else {
      await connectDB();
    }
  } catch (error) {
    console.error('数据库连接失败:', error);
  }
};

// 优雅关闭数据库连接
const closeConnection = async () => {
  try {
    await mongoose.connection.close();
    console.log('数据库连接已关闭');
  } catch (error) {
    console.error('关闭数据库连接失败:', error);
  }
};

module.exports = {
  connectDB,
  testConnection,
  closeConnection,
  mongoose
};
