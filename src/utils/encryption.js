const CryptoJS = require('crypto-js');
require('dotenv').config();

/**
 * AES 加密工具类
 */
class EncryptionUtil {
  /**
   * 使用 AES-ECB 加密文本
   * @param {string} text - 要加密的文本
   * @returns {string} - 加密后的文本（十六进制编码）
   */
  static encryptAES(text) {
    try {
      // 从环境变量获取密钥
      const secretKey = process.env.AES_SECRET_KEY;

      if (!secretKey) {
        throw new Error('加密密钥未配置');
      }

      // 将密钥转换为 WordArray
      const key = CryptoJS.enc.Utf8.parse(secretKey);

      // 使用 ECB 模式加密（ECB 模式不需要 IV）
      const encrypted = CryptoJS.AES.encrypt(text, key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
      });

      // 将加密结果转换为十六进制格式
      return encrypted.ciphertext.toString(CryptoJS.enc.Hex);
    } catch (error) {
      console.error('AES-ECB 加密失败:', error);
      throw error;
    }
  }

  /**
   * 使用 AES-ECB 解密文本
   * @param {string} encryptedHex - 要解密的文本（十六进制编码）
   * @returns {string} - 解密后的文本
   */
  static decryptAES(encryptedHex) {
    try {
      // 从环境变量获取密钥
      const secretKey = process.env.AES_SECRET_KEY;

      if (!secretKey) {
        throw new Error('加密密钥未配置');
      }

      // 将密钥转换为 WordArray
      const key = CryptoJS.enc.Utf8.parse(secretKey);

      // 将十六进制字符串转换为 CipherParams 对象
      const cipherParams = CryptoJS.lib.CipherParams.create({
        ciphertext: CryptoJS.enc.Hex.parse(encryptedHex)
      });

      // 使用 ECB 模式解密
      const decrypted = CryptoJS.AES.decrypt(cipherParams, key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7
      });

      // 返回 UTF-8 编码的解密结果
      return decrypted.toString(CryptoJS.enc.Utf8);
    } catch (error) {
      console.error('AES-ECB 解密失败:', error);
      throw error;
    }
  }
}

module.exports = EncryptionUtil;
