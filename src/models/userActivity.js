const mongoose = require('mongoose');

// 用户活动Schema
const userActivitySchema = new mongoose.Schema({
  user_name: {
    type: String,
    required: true,
    maxlength: 100,
    comment: '用户名',
    index: true
  },
  prompt: {
    type: String,
    default: '',
    comment: '用户提示词'
  },
  ide: {
    type: String,
    default: '',
    maxlength: 50,
    comment: '用户使用的IDE'
  },
  mcp_name: {
    type: String,
    default: '',
    maxlength: 100,
    comment: 'MCP名称',
  },
  file_path: {
    type: String,
    default: '',
    maxlength: 200,
    comment: '文件相对路径'
  },
  git_store: {
    type: String,
    default: '',
    maxlength: 500,
    comment: 'git仓库路径'
  },
  detail: {
    type: mongoose.Schema.Types.Map,
    default: {},
    comment: '详细信息'
  },
}, {
  timestamps: true,
  collection: 'user_activity'
});

// 创建索引
userActivitySchema.index({ mcp_name: 1, createAt : 1 });
// userActivitySchema.index({ createdAt: -1 });

const UserActivity = mongoose.model('UserActivity', userActivitySchema);

module.exports = UserActivity;
