// Rename this file to user.js
const mongoose = require('mongoose');

// 用户Schema
const userSchema = new mongoose.Schema({
  user_name: {
    type: String,
    required: true,
    maxlength: 100,
    comment: '用户名',
    index: true,
    unique: true
  },
  user_tl: {
    type: String,
    required: false,
    default: '',
    maxlength: 100,
    comment: '用户组长'
  },
  effect_time: {
    type: Date,
    required: true,
    default: Date.now,
    comment: '生效时间'
  }
}, {
  timestamps: true,
  collection: 'users'
});

// 创建索引
// userSchema.index({ user_name: 1 });
// userSchema.index({ effect_time: -1 });

const User = mongoose.model('User', userSchema);

module.exports = User;
