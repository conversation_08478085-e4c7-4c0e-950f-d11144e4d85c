const mongoose = require('mongoose');

// 用户组Schema
const userGroupSchema = new mongoose.Schema({
  user_name: {
    type: String,
    required: true,
    maxlength: 100,
    comment: '用户名'
  },
  user_tl: {
    type: String,
    required: true,
    default: '',
    maxlength: 100,
    comment: '用户组长'
  },
  effect_time: {
    type: Date,
    required: true,
    default: Date.now,
    comment: '生效时间'
  }
}, {
  timestamps: true,
  collection: 'user_groups'
});

// 创建索引
userGroupSchema.index({ user_name: 1 });
userGroupSchema.index({ effect_time: -1 });

const UserGroup = mongoose.model('UserGroup', userGroupSchema);

module.exports = UserGroup;
